# 🧠 SmartEye Device Hub

**SmartEye Device Hub** is a standalone microservice responsible for managing real-time device communication using the **MQTT protocol**.  
It acts as the middleware between the **SmartEye Cloud (Django backend + UI)** and **IoT devices (Smart Pump, Smart Tank, Smart Solar)**.  

The service subscribes to MQTT topics, receives telemetry or sales data from field devices, and publishes configuration updates back to them when necessary.

---

## 📜 Project Overview

| Component | Description |
|------------|--------------|
| **Service Name** | `smarteye-device-hub` |
| **Purpose** | Handle MQTT message communication between SmartEye devices and cloud |
| **Protocol** | MQTT over TCP/TLS |
| **Framework** | FastAPI (Python 3.11+) |
| **Database** | MySQL (for device logs & local cache, if needed) |
| **Broker** | Mosquitto (MQTT Broker) |
| **Integration** | Django (SmartEye Backend) via REST APIs or Webhooks |

---

## 🧩 Core Responsibilities

1. **Subscribe** to MQTT topics for devices (e.g., config updates, telemetry, and status).
2. **Publish** configuration data from the cloud to devices.
3. **Store** device messages and statuses locally or forward them to the main Django backend.
4. **Bridge** FastAPI endpoints for administrative or monitoring actions.
5. **Ensure** message delivery reliability with retries and acknowledgment handling.

---

## ⚙️ System Architecture

```
            ┌────────────────────┐
            │  SmartEye UI (React)│
            └──────────┬─────────┘
                       │ REST API
                       ▼
              ┌───────────────────┐
              │  SmartEye Backend │  (Django)
              └───────┬───────────┘
                      │
               MQTT Config Push (via REST)
                      │
                      ▼
          ┌────────────────────────┐
          │   SmartEye Device Hub   │  (FastAPI + AioMQTT)
          └─────────┬──────────────┘
                    │
            MQTT Publish / Subscribe
                    │
                    ▼
            ┌────────────────────┐
            │   Smart Devices     │
            │ (Pump, Tank, Solar) │
            └────────────────────┘
```

---

## 🧠 Key Topics and Message Flow

| Action | Topic Example | Direction | Description |
|---------|----------------|------------|--------------|
| Publish Config | `company/{company_id}/device/{device_id}/config` | Cloud → Device | Sends latest device configuration |
| Report Sales | `company/{company_id}/device/{device_id}/sales` | Device → Cloud | Pushes fuel sale transaction data |
| Health Check | `company/{company_id}/device/{device_id}/status` | Device ↔ Hub | Keeps device connection alive |
| Tank Level Update | `company/{company_id}/device/{device_id}/tank_log` | Device → Cloud | Sends tank telemetry data |

---

## 🚀 Installation & Setup

### 1. Clone the repository

```bash
git clone https://github.com/<your-org>/smarteye-device-hub.git
cd smarteye-device-hub
```

### 2. Create a Python virtual environment

```bash
python3 -m venv .venv
source .venv/bin/activate
```

### 3. Install dependencies

```bash
pip install -r requirements.txt
```

### 4. Configure environment variables

Create a `.env` file in the project root:

```env
# MQTT Broker
MQTT_BROKER_HOST=mqtt.smarteye.org
MQTT_BROKER_PORT=1883
MQTT_USERNAME=smarteye
MQTT_PASSWORD=your_password
MQTT_KEEPALIVE=60

# FastAPI App
APP_ENV=development
APP_PORT=8000

# Django REST Backend (Cloud)
BACKEND_BASE_URL=https://api.smarteye.smartflowtech.org
BACKEND_API_KEY=your_api_key

# Database (optional)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=smarteye_devicehub
DB_USER=root
DB_PASSWORD=password
```

---

## 🧠 Running the Application

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

You should see logs similar to:

```
INFO:     Started reloader process [12345] using StatReload
INFO:     Application startup complete.
INFO:     Connected to MQTT broker mqtt.smarteye.org:1883
```

---

## 🧩 Example MQTT Handler Flow

**Incoming Telemetry:**
```python
@mqtt_client.on_message("company/+/device/+/sales")
async def handle_sales_message(topic, payload):
    data = json.loads(payload)
    await forward_to_backend(data)
```

**Publish Configuration:**
```python
await mqtt_client.publish(
    f"company/{company_id}/device/{device_id}/config",
    json.dumps(config_data)
)
```

---

## 🧪 Testing the Service

Use `mosquitto_pub` and `mosquitto_sub` for manual testing:

```bash
# Subscribe to sales topic
mosquitto_sub -h mqtt.smarteye.org -t "company/325/device/550/sales"

# Publish configuration
mosquitto_pub -h mqtt.smarteye.org -t "company/325/device/550/config" -m '{"price": 850}'
```

---

## 📁 Project Structure

```
smarteye-device-hub/
├── main.py                   # FastAPI app entry point
├── mqtt/
│   ├── client.py             # MQTT connection and handlers
│   ├── topics.py             # Topic definitions
│   └── handlers/
│       ├── pump.py           # Pump-related logic
│       ├── tank.py           # Tank telemetry handlers
│       └── solar.py          # Solar monitoring logic
├── services/
│   ├── backend_sync.py       # Sync with Django REST API
│   ├── config_manager.py     # Configuration caching and updates
│   └── db.py                 # Database helper functions
├── .env.example
├── requirements.txt
└── README.md
```

---

## 🧭 Next Steps

- [ ] Implement MQTT connection lifecycle (`connect`, `disconnect`, `reconnect`)
- [ ] Add structured logging for message tracking
- [ ] Add async job queue for device message retries
- [ ] Integrate REST sync service with Django backend
- [ ] Secure endpoints with API keys or JWT

---

## 🧑‍💻 Author
Smartflow Technologies  
💻 Python | FastAPI | MQTT | Django | IoT  
