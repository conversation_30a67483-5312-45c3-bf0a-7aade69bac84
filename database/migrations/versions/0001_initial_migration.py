"""Initial migration - Create all tables

Revision ID: 0001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create companies table
    op.create_table('companies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('code', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code')
    )
    op.create_index(op.f('ix_companies_id'), 'companies', ['id'], unique=False)

    # Create sites table
    op.create_table('sites',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('code', sa.String(length=50), nullable=False),
        sa.Column('company_id', sa.Integer(), nullable=False),
        sa.Column('location', sa.String(length=500), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sites_id'), 'sites', ['id'], unique=False)

    # Create devices table
    op.create_table('devices',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('device_id', sa.String(length=100), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('device_type', sa.String(length=50), nullable=False),
        sa.Column('site_id', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('last_seen', sa.DateTime(timezone=True), nullable=True),
        sa.Column('configuration', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['site_id'], ['sites.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('device_id')
    )
    op.create_index(op.f('ix_devices_device_id'), 'devices', ['device_id'], unique=False)
    op.create_index(op.f('ix_devices_id'), 'devices', ['id'], unique=False)

    # Create tanks table
    op.create_table('tanks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('tank_id', sa.String(length=100), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('device_id', sa.Integer(), nullable=False),
        sa.Column('capacity', sa.Float(), nullable=True),
        sa.Column('fuel_type', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['device_id'], ['devices.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tank_id')
    )
    op.create_index(op.f('ix_tanks_id'), 'tanks', ['id'], unique=False)
    op.create_index(op.f('ix_tanks_tank_id'), 'tanks', ['tank_id'], unique=False)

    # Create pumps table
    op.create_table('pumps',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('pump_id', sa.String(length=100), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('device_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['device_id'], ['devices.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('pump_id')
    )
    op.create_index(op.f('ix_pumps_id'), 'pumps', ['id'], unique=False)
    op.create_index(op.f('ix_pumps_pump_id'), 'pumps', ['pump_id'], unique=False)

    # Create nozzles table
    op.create_table('nozzles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('nozzle_id', sa.String(length=100), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('pump_id', sa.Integer(), nullable=False),
        sa.Column('fuel_type', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['pump_id'], ['pumps.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('nozzle_id')
    )
    op.create_index(op.f('ix_nozzles_id'), 'nozzles', ['id'], unique=False)
    op.create_index(op.f('ix_nozzles_nozzle_id'), 'nozzles', ['nozzle_id'], unique=False)

    # Create tank_logs table
    op.create_table('tank_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('tank_id', sa.Integer(), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('volume', sa.Float(), nullable=True),
        sa.Column('temperature', sa.Float(), nullable=True),
        sa.Column('water_level', sa.Float(), nullable=True),
        sa.Column('fuel_height', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['tank_id'], ['tanks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tank_logs_id'), 'tank_logs', ['id'], unique=False)
    op.create_index(op.f('ix_tank_logs_timestamp'), 'tank_logs', ['timestamp'], unique=False)
    op.create_index(op.f('ix_tank_logs_tank_id'), 'tank_logs', ['tank_id'], unique=False)

    # Create pump_logs table
    op.create_table('pump_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('pump_id', sa.Integer(), nullable=False),
        sa.Column('nozzle_id', sa.String(length=100), nullable=False),
        sa.Column('transaction_id', sa.String(length=100), nullable=False),
        sa.Column('start_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
        sa.Column('volume_dispensed', sa.Float(), nullable=True),
        sa.Column('amount', sa.Float(), nullable=True),
        sa.Column('price_per_liter', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['pump_id'], ['pumps.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('transaction_id')
    )
    op.create_index(op.f('ix_pump_logs_id'), 'pump_logs', ['id'], unique=False)
    op.create_index(op.f('ix_pump_logs_start_time'), 'pump_logs', ['start_time'], unique=False)
    op.create_index(op.f('ix_pump_logs_pump_id'), 'pump_logs', ['pump_id'], unique=False)
    op.create_index(op.f('ix_pump_logs_transaction_id'), 'pump_logs', ['transaction_id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('pump_logs')
    op.drop_table('tank_logs')
    op.drop_table('nozzles')
    op.drop_table('pumps')
    op.drop_table('tanks')
    op.drop_table('devices')
    op.drop_table('sites')
    op.drop_table('companies')
