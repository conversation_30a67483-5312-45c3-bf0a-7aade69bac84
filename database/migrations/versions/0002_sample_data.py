"""Add sample data for testing

Revision ID: 0002
Revises: 0001
Create Date: 2024-01-01 01:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '0002'
down_revision = '0001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create sample companies
    companies_table = sa.table('companies',
        sa.column('id', sa.Integer),
        sa.column('name', sa.String),
        sa.column('code', sa.String),
        sa.column('created_at', sa.DateTime)
    )
    
    op.bulk_insert(companies_table, [
        {
            'id': 1,
            'name': 'Smarteye Petroleum Ltd',
            'code': 'SMARTEYE',
            'created_at': datetime.now()
        },
        {
            'id': 2,
            'name': 'Demo Fuel Company',
            'code': 'DEMO',
            'created_at': datetime.now()
        }
    ])
    
    # Create sample sites
    sites_table = sa.table('sites',
        sa.column('id', sa.Integer),
        sa.column('name', sa.String),
        sa.column('code', sa.String),
        sa.column('company_id', sa.Integer),
        sa.column('location', sa.String),
        sa.column('created_at', sa.DateTime)
    )
    
    op.bulk_insert(sites_table, [
        {
            'id': 1,
            'name': 'Main Station Lagos',
            'code': 'MAIN_LOS',
            'company_id': 1,
            'location': 'Victoria Island, Lagos, Nigeria',
            'created_at': datetime.now()
        },
        {
            'id': 2,
            'name': 'Branch Station Abuja',
            'code': 'BRANCH_ABJ',
            'company_id': 1,
            'location': 'Central Business District, Abuja, Nigeria',
            'created_at': datetime.now()
        },
        {
            'id': 3,
            'name': 'Demo Station',
            'code': 'DEMO_SITE',
            'company_id': 2,
            'location': 'Demo Location',
            'created_at': datetime.now()
        }
    ])
    
    # Create sample devices
    devices_table = sa.table('devices',
        sa.column('id', sa.Integer),
        sa.column('device_id', sa.String),
        sa.column('name', sa.String),
        sa.column('device_type', sa.String),
        sa.column('site_id', sa.Integer),
        sa.column('is_active', sa.Boolean),
        sa.column('configuration', sa.JSON),
        sa.column('created_at', sa.DateTime)
    )
    
    op.bulk_insert(devices_table, [
        {
            'id': 1,
            'device_id': 'TANK_001_LOS',
            'name': 'Tank 1 - Premium Motor Spirit',
            'device_type': 'tank',
            'site_id': 1,
            'is_active': True,
            'configuration': '{"capacity": 50000, "fuel_type": "PMS", "sensor_type": "ATG"}',
            'created_at': datetime.now()
        },
        {
            'id': 2,
            'device_id': 'TANK_002_LOS',
            'name': 'Tank 2 - Automotive Gas Oil',
            'device_type': 'tank',
            'site_id': 1,
            'is_active': True,
            'configuration': '{"capacity": 45000, "fuel_type": "AGO", "sensor_type": "ATG"}',
            'created_at': datetime.now()
        },
        {
            'id': 3,
            'device_id': 'PUMP_001_LOS',
            'name': 'Pump 1 - Island A',
            'device_type': 'pump',
            'site_id': 1,
            'is_active': True,
            'configuration': '{"nozzle_count": 4, "payment_methods": ["cash", "card", "mobile"]}',
            'created_at': datetime.now()
        },
        {
            'id': 4,
            'device_id': 'PUMP_002_LOS',
            'name': 'Pump 2 - Island A',
            'device_type': 'pump',
            'site_id': 1,
            'is_active': True,
            'configuration': '{"nozzle_count": 4, "payment_methods": ["cash", "card", "mobile"]}',
            'created_at': datetime.now()
        },
        {
            'id': 5,
            'device_id': 'TANK_001_ABJ',
            'name': 'Tank 1 - Premium Motor Spirit',
            'device_type': 'tank',
            'site_id': 2,
            'is_active': True,
            'configuration': '{"capacity": 40000, "fuel_type": "PMS", "sensor_type": "ATG"}',
            'created_at': datetime.now()
        },
        {
            'id': 6,
            'device_id': 'PUMP_001_ABJ',
            'name': 'Pump 1 - Main Island',
            'device_type': 'pump',
            'site_id': 2,
            'is_active': True,
            'configuration': '{"nozzle_count": 6, "payment_methods": ["cash", "card"]}',
            'created_at': datetime.now()
        }
    ])
    
    # Create sample tanks
    tanks_table = sa.table('tanks',
        sa.column('id', sa.Integer),
        sa.column('tank_id', sa.String),
        sa.column('name', sa.String),
        sa.column('device_id', sa.Integer),
        sa.column('capacity', sa.Float),
        sa.column('fuel_type', sa.String),
        sa.column('created_at', sa.DateTime)
    )
    
    op.bulk_insert(tanks_table, [
        {
            'id': 1,
            'tank_id': 'TANK_001_LOS',
            'name': 'Tank 1 - Premium Motor Spirit',
            'device_id': 1,
            'capacity': 50000.0,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        },
        {
            'id': 2,
            'tank_id': 'TANK_002_LOS',
            'name': 'Tank 2 - Automotive Gas Oil',
            'device_id': 2,
            'capacity': 45000.0,
            'fuel_type': 'AGO',
            'created_at': datetime.now()
        },
        {
            'id': 3,
            'tank_id': 'TANK_001_ABJ',
            'name': 'Tank 1 - Premium Motor Spirit',
            'device_id': 5,
            'capacity': 40000.0,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        }
    ])
    
    # Create sample pumps
    pumps_table = sa.table('pumps',
        sa.column('id', sa.Integer),
        sa.column('pump_id', sa.String),
        sa.column('name', sa.String),
        sa.column('device_id', sa.Integer),
        sa.column('created_at', sa.DateTime)
    )
    
    op.bulk_insert(pumps_table, [
        {
            'id': 1,
            'pump_id': 'PUMP_001_LOS',
            'name': 'Pump 1 - Island A',
            'device_id': 3,
            'created_at': datetime.now()
        },
        {
            'id': 2,
            'pump_id': 'PUMP_002_LOS',
            'name': 'Pump 2 - Island A',
            'device_id': 4,
            'created_at': datetime.now()
        },
        {
            'id': 3,
            'pump_id': 'PUMP_001_ABJ',
            'name': 'Pump 1 - Main Island',
            'device_id': 6,
            'created_at': datetime.now()
        }
    ])
    
    # Create sample nozzles
    nozzles_table = sa.table('nozzles',
        sa.column('id', sa.Integer),
        sa.column('nozzle_id', sa.String),
        sa.column('name', sa.String),
        sa.column('pump_id', sa.Integer),
        sa.column('fuel_type', sa.String),
        sa.column('created_at', sa.DateTime)
    )
    
    op.bulk_insert(nozzles_table, [
        # Pump 1 nozzles
        {
            'id': 1,
            'nozzle_id': 'NOZZLE_001_P001_LOS',
            'name': 'Nozzle 1 - PMS',
            'pump_id': 1,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        },
        {
            'id': 2,
            'nozzle_id': 'NOZZLE_002_P001_LOS',
            'name': 'Nozzle 2 - PMS',
            'pump_id': 1,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        },
        {
            'id': 3,
            'nozzle_id': 'NOZZLE_003_P001_LOS',
            'name': 'Nozzle 3 - AGO',
            'pump_id': 1,
            'fuel_type': 'AGO',
            'created_at': datetime.now()
        },
        {
            'id': 4,
            'nozzle_id': 'NOZZLE_004_P001_LOS',
            'name': 'Nozzle 4 - AGO',
            'pump_id': 1,
            'fuel_type': 'AGO',
            'created_at': datetime.now()
        },
        # Pump 2 nozzles
        {
            'id': 5,
            'nozzle_id': 'NOZZLE_001_P002_LOS',
            'name': 'Nozzle 1 - PMS',
            'pump_id': 2,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        },
        {
            'id': 6,
            'nozzle_id': 'NOZZLE_002_P002_LOS',
            'name': 'Nozzle 2 - PMS',
            'pump_id': 2,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        },
        # Abuja pump nozzles
        {
            'id': 7,
            'nozzle_id': 'NOZZLE_001_P001_ABJ',
            'name': 'Nozzle 1 - PMS',
            'pump_id': 3,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        },
        {
            'id': 8,
            'nozzle_id': 'NOZZLE_002_P001_ABJ',
            'name': 'Nozzle 2 - PMS',
            'pump_id': 3,
            'fuel_type': 'PMS',
            'created_at': datetime.now()
        }
    ])


def downgrade() -> None:
    # Delete sample data in reverse order
    op.execute("DELETE FROM nozzles WHERE id IN (1,2,3,4,5,6,7,8)")
    op.execute("DELETE FROM pumps WHERE id IN (1,2,3)")
    op.execute("DELETE FROM tanks WHERE id IN (1,2,3)")
    op.execute("DELETE FROM devices WHERE id IN (1,2,3,4,5,6)")
    op.execute("DELETE FROM sites WHERE id IN (1,2,3)")
    op.execute("DELETE FROM companies WHERE id IN (1,2)")
