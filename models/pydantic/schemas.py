from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum

class DeviceType(str, Enum):
    TANK = "tank"
    PUMP = "pump"

class TransactionStatus(str, Enum):
    STARTED = "started"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

# Base schemas
class CompanyBase(BaseModel):
    name: str
    code: str

class CompanyCreate(CompanyBase):
    pass

class Company(CompanyBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class SiteBase(BaseModel):
    name: str
    code: str
    company_id: int
    location: Optional[str] = None

class SiteCreate(SiteBase):
    pass

class Site(SiteBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class DeviceBase(BaseModel):
    device_id: str
    name: str
    device_type: DeviceType
    site_id: int
    is_active: bool = True
    configuration: Optional[Dict[str, Any]] = None

class DeviceCreate(DeviceBase):
    pass

class DeviceUpdate(BaseModel):
    name: Optional[str] = None
    is_active: Optional[bool] = None
    configuration: Optional[Dict[str, Any]] = None

class Device(DeviceBase):
    id: int
    last_seen: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class TankBase(BaseModel):
    tank_id: str
    name: str
    device_id: int
    capacity: Optional[float] = None
    fuel_type: Optional[str] = None

class TankCreate(TankBase):
    pass

class Tank(TankBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class PumpBase(BaseModel):
    pump_id: str
    name: str
    device_id: int

class PumpCreate(PumpBase):
    pass

class Pump(PumpBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class NozzleBase(BaseModel):
    nozzle_id: str
    name: str
    pump_id: int
    fuel_type: Optional[str] = None

class NozzleCreate(NozzleBase):
    pass

class Nozzle(NozzleBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class TankLogBase(BaseModel):
    tank_id: int
    timestamp: datetime
    volume: Optional[float] = None
    temperature: Optional[float] = None
    water_level: Optional[float] = None
    fuel_height: Optional[float] = None

class TankLogCreate(TankLogBase):
    pass

class TankLog(TankLogBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class PumpLogBase(BaseModel):
    pump_id: int
    nozzle_id: str
    transaction_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    volume_dispensed: Optional[float] = None
    amount: Optional[float] = None
    price_per_liter: Optional[float] = None
    status: TransactionStatus = TransactionStatus.STARTED

class PumpLogCreate(PumpLogBase):
    pass

class PumpLogUpdate(BaseModel):
    end_time: Optional[datetime] = None
    volume_dispensed: Optional[float] = None
    amount: Optional[float] = None
    price_per_liter: Optional[float] = None
    status: Optional[TransactionStatus] = None

class PumpLog(PumpLogBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

# MQTT Message schemas
class MQTTMessage(BaseModel):
    topic: str
    payload: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)

class DeviceConfigMessage(BaseModel):
    device_id: str
    configuration: Dict[str, Any]

class TankLogMessage(BaseModel):
    tank_id: str
    timestamp: datetime
    volume: Optional[float] = None
    temperature: Optional[float] = None
    water_level: Optional[float] = None
    fuel_height: Optional[float] = None

class PumpLogMessage(BaseModel):
    pump_id: str
    nozzle_id: str
    transaction_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    volume_dispensed: Optional[float] = None
    amount: Optional[float] = None
    price_per_liter: Optional[float] = None
    status: TransactionStatus = TransactionStatus.STARTED
