from sqlalchemy import Column, Integer, String, DateTime, Float, Boolean, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.connection import Base

class Company(Base):
    __tablename__ = "companies"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    code = Column(String(50), unique=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    sites = relationship("Site", back_populates="company")

class Site(Base):
    __tablename__ = "sites"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    code = Column(String(50), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    location = Column(String(500))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    company = relationship("Company", back_populates="sites")
    devices = relationship("Device", back_populates="site")

class Device(Base):
    __tablename__ = "devices"
    
    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String(100), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    device_type = Column(String(50), nullable=False)  # 'tank', 'pump', etc.
    site_id = Column(Integer, ForeignKey("sites.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    last_seen = Column(DateTime(timezone=True))
    configuration = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    site = relationship("Site", back_populates="devices")
    tanks = relationship("Tank", back_populates="device")
    pumps = relationship("Pump", back_populates="device")

class Tank(Base):
    __tablename__ = "tanks"
    
    id = Column(Integer, primary_key=True, index=True)
    tank_id = Column(String(100), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=False)
    capacity = Column(Float)  # Tank capacity in liters
    fuel_type = Column(String(50))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    device = relationship("Device", back_populates="tanks")
    tank_logs = relationship("TankLog", back_populates="tank")

class Pump(Base):
    __tablename__ = "pumps"
    
    id = Column(Integer, primary_key=True, index=True)
    pump_id = Column(String(100), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    device_id = Column(Integer, ForeignKey("devices.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    device = relationship("Device", back_populates="pumps")
    nozzles = relationship("Nozzle", back_populates="pump")
    pump_logs = relationship("PumpLog", back_populates="pump")

class Nozzle(Base):
    __tablename__ = "nozzles"
    
    id = Column(Integer, primary_key=True, index=True)
    nozzle_id = Column(String(100), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    pump_id = Column(Integer, ForeignKey("pumps.id"), nullable=False)
    fuel_type = Column(String(50))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    pump = relationship("Pump", back_populates="nozzles")

class TankLog(Base):
    __tablename__ = "tank_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    tank_id = Column(Integer, ForeignKey("tanks.id"), nullable=False)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    volume = Column(Float)  # Current volume in liters
    temperature = Column(Float)  # Temperature in Celsius
    water_level = Column(Float)  # Water level in mm
    fuel_height = Column(Float)  # Fuel height in mm
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    tank = relationship("Tank", back_populates="tank_logs")

class PumpLog(Base):
    __tablename__ = "pump_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    pump_id = Column(Integer, ForeignKey("pumps.id"), nullable=False)
    nozzle_id = Column(String(100), nullable=False)
    transaction_id = Column(String(100), unique=True, nullable=False)
    start_time = Column(DateTime(timezone=True), nullable=False)
    end_time = Column(DateTime(timezone=True))
    volume_dispensed = Column(Float)  # Volume in liters
    amount = Column(Float)  # Amount in currency
    price_per_liter = Column(Float)
    status = Column(String(50))  # 'started', 'completed', 'cancelled'
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    pump = relationship("Pump", back_populates="pump_logs")
