"""MQTT topic definitions and utilities"""


class MQTTTopics:
    """MQTT topic patterns for the Smarteye system"""

    # Base topic prefix
    BASE = "smarteye"

    # Device configuration topics
    DEVICE_CONFIG = f"{BASE}/pumps/{{mac_address}}/config"

    # Tank topics
    TANK_LOGS = f"{BASE}/tanks_logs"
    TANK_EVENTS = f"{BASE}/tanks/{{tank_id}}/events"
    TANK_STATUS = f"{BASE}/tanks/{{tank_id}}/status"

    # Pump topics
    PUMP_LOGS = f"{BASE}/pump_logs"
    PUMP_EVENTS = f"{BASE}/pumps/{{pump_id}}/events"
    PUMP_STATUS = f"{BASE}/pumps/{{pump_id}}/status"

    # Nozzle topics
    NOZZLE_STATUS = f"{BASE}/nozzles/{{nozzle_id}}/status"
    NOZZLE_EVENTS = f"{BASE}/nozzles/{{nozzle_id}}/events"

    # System topics
    SYSTEM_STATUS = f"{BASE}/system/status"
    SYSTEM_ALERTS = f"{BASE}/system/alerts"


def get_device_config_topic(device_id: str) -> str:
    """Get device configuration topic for specific device"""
    return MQTTTopics.DEVICE_CONFIG.format(device_id=device_id)


def get_tank_logs_topic(tank_id: str) -> str:
    """Get tank logs topic for specific tank"""
    return MQTTTopics.TANK_LOGS.format(tank_id=tank_id)


def get_pump_logs_topic(pump_id: str) -> str:
    """Get pump logs topic for specific pump"""
    return MQTTTopics.PUMP_LOGS.format(pump_id=pump_id)


def parse_topic_device_id(topic: str) -> str:
    """Extract device ID from topic"""
    parts = topic.split('/')
    if len(parts) >= 3 and parts[0] == "smarteye" and parts[1] == "devices":
        return parts[2]
    return ""


def parse_topic_tank_id(topic: str) -> str:
    """Extract tank ID from topic"""
    parts = topic.split('/')
    if len(parts) >= 3 and parts[0] == "smarteye" and parts[1] == "tanks":
        return parts[2]
    return ""


def parse_topic_pump_id(topic: str) -> str:
    """Extract pump ID from topic"""
    parts = topic.split('/')
    if len(parts) >= 3 and parts[0] == "smarteye" and parts[1] == "pumps":
        return parts[2]
    return ""


def parse_topic_nozzle_id(topic: str) -> str:
    """Extract nozzle ID from topic"""
    parts = topic.split('/')
    if len(parts) >= 3 and parts[0] == "smarteye" and parts[1] == "nozzles":
        return parts[2]
    return ""
