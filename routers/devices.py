from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from database.connection import get_db
from models.pydantic.schemas import Device, DeviceCreate, DeviceUpdate
from services.device_service import DeviceService

router = APIRouter()

@router.get("/", response_model=List[Device])
async def get_devices(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get all devices with pagination"""
    device_service = DeviceService(db)
    devices = await device_service.get_devices(skip=skip, limit=limit)
    return devices

@router.get("/{device_id}", response_model=Device)
async def get_device(
    device_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific device by device_id"""
    device_service = DeviceService(db)
    device = await device_service.get_device_by_device_id(device_id)
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    return device

@router.post("/", response_model=Device, status_code=status.HTTP_201_CREATED)
async def create_device(
    device: DeviceCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new device"""
    device_service = DeviceService(db)
    
    # Check if device already exists
    existing_device = await device_service.get_device_by_device_id(device.device_id)
    if existing_device:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Device with this device_id already exists"
        )
    
    return await device_service.create_device(device)

@router.put("/{device_id}", response_model=Device)
async def update_device(
    device_id: str,
    device_update: DeviceUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update a device"""
    device_service = DeviceService(db)
    
    device = await device_service.get_device_by_device_id(device_id)
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    return await device_service.update_device(device.id, device_update)

@router.delete("/{device_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_device(
    device_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Delete a device"""
    device_service = DeviceService(db)
    
    device = await device_service.get_device_by_device_id(device_id)
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    await device_service.delete_device(device.id)

@router.get("/{device_id}/config")
async def get_device_config(
    device_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get device configuration"""
    device_service = DeviceService(db)
    
    device = await device_service.get_device_by_device_id(device_id)
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    return device.configuration or {}

@router.post("/{device_id}/config")
async def update_device_config(
    device_id: str,
    config: dict,
    db: AsyncSession = Depends(get_db)
):
    """Update device configuration and publish to MQTT"""
    device_service = DeviceService(db)
    
    device = await device_service.get_device_by_device_id(device_id)
    if not device:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Device not found"
        )
    
    # Update device configuration in database
    device_update = DeviceUpdate(configuration=config)
    updated_device = await device_service.update_device(device.id, device_update)
    
    # Publish configuration to MQTT
    from mqtt.client import mqtt_client
    await mqtt_client.publish_device_config(device_id, config)
    
    return {"message": "Device configuration updated and published", "config": config}
