from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any

from mqtt.client import mqtt_client
from mqtt.topics import get_device_config_topic, get_tank_logs_topic, get_pump_logs_topic
from models.pydantic.schemas import MQTTMessage, DeviceConfigMessage

router = APIRouter()

@router.post("/publish")
async def publish_message(message: MQTTMessage):
    """Publish a message to MQTT broker"""
    success = await mqtt_client.publish(
        topic=message.topic,
        payload=message.payload,
        qos=0,
        retain=False
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish message to MQTT broker"
        )
    
    return {"message": "Message published successfully", "topic": message.topic}

@router.post("/publish/device-config")
async def publish_device_config(config_message: DeviceConfigMessage):
    """Publish device configuration to MQTT"""
    topic = get_device_config_topic(config_message.device_id)
    
    success = await mqtt_client.publish(
        topic=topic,
        payload=config_message.configuration,
        qos=1,  # QoS 1 for device config to ensure delivery
        retain=True  # Retain config messages
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish device configuration"
        )
    
    return {
        "message": "Device configuration published successfully",
        "device_id": config_message.device_id,
        "topic": topic
    }

@router.get("/status")
async def get_mqtt_status():
    """Get MQTT client connection status"""
    return {
        "connected": mqtt_client.is_connected,
        "broker_host": mqtt_client.host,
        "broker_port": mqtt_client.port
    }

@router.post("/test/tank-log/{tank_id}")
async def test_tank_log_publish(tank_id: str, log_data: Dict[str, Any]):
    """Test endpoint to publish tank log data"""
    topic = get_tank_logs_topic(tank_id)
    
    success = await mqtt_client.publish(
        topic=topic,
        payload=log_data,
        qos=0,
        retain=False
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish tank log"
        )
    
    return {
        "message": "Tank log published successfully",
        "tank_id": tank_id,
        "topic": topic,
        "data": log_data
    }

@router.post("/test/pump-log/{pump_id}")
async def test_pump_log_publish(pump_id: str, log_data: Dict[str, Any]):
    """Test endpoint to publish pump log data"""
    topic = get_pump_logs_topic(pump_id)
    
    success = await mqtt_client.publish(
        topic=topic,
        payload=log_data,
        qos=0,
        retain=False
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to publish pump log"
        )
    
    return {
        "message": "Pump log published successfully",
        "pump_id": pump_id,
        "topic": topic,
        "data": log_data
    }
