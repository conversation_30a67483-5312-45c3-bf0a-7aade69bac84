from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from datetime import datetime

from database.connection import get_db
from models.pydantic.schemas import Pump, PumpCreate, PumpLog, PumpLogCreate, Nozzle, NozzleCreate
from services.device_service import DeviceService
from services.data_service import DataService

router = APIRouter()

@router.get("/", response_model=List[Pump])
async def get_pumps(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get all pumps with pagination"""
    device_service = DeviceService(db)
    pumps = await device_service.get_pumps(skip=skip, limit=limit)
    return pumps

@router.get("/{pump_id}", response_model=Pump)
async def get_pump(
    pump_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific pump by pump_id"""
    device_service = DeviceService(db)
    pump = await device_service.get_pump_by_pump_id(pump_id)
    if not pump:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pump not found"
        )
    return pump

@router.post("/", response_model=Pump, status_code=status.HTTP_201_CREATED)
async def create_pump(
    pump: PumpCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new pump"""
    device_service = DeviceService(db)
    
    # Check if pump already exists
    existing_pump = await device_service.get_pump_by_pump_id(pump.pump_id)
    if existing_pump:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Pump with this pump_id already exists"
        )
    
    return await device_service.create_pump(pump)

@router.get("/{pump_id}/nozzles", response_model=List[Nozzle])
async def get_pump_nozzles(
    pump_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get all nozzles for a specific pump"""
    device_service = DeviceService(db)
    
    # Verify pump exists
    pump = await device_service.get_pump_by_pump_id(pump_id)
    if not pump:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pump not found"
        )
    
    nozzles = await device_service.get_nozzles_by_pump_id(pump.id)
    return nozzles

@router.post("/{pump_id}/nozzles", response_model=Nozzle, status_code=status.HTTP_201_CREATED)
async def create_nozzle(
    pump_id: str,
    nozzle: NozzleCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new nozzle for a pump"""
    device_service = DeviceService(db)
    
    # Verify pump exists
    pump = await device_service.get_pump_by_pump_id(pump_id)
    if not pump:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pump not found"
        )
    
    # Set pump_id from URL parameter
    nozzle.pump_id = pump.id
    
    return await device_service.create_nozzle(nozzle)

@router.get("/{pump_id}/logs", response_model=List[PumpLog])
async def get_pump_logs(
    pump_id: str,
    start_time: Optional[datetime] = Query(None, description="Start time for log filtering"),
    end_time: Optional[datetime] = Query(None, description="End time for log filtering"),
    status_filter: Optional[str] = Query(None, description="Filter by transaction status"),
    skip: int = 0,
    limit: int = 1000,
    db: AsyncSession = Depends(get_db)
):
    """Get pump logs with optional filtering"""
    device_service = DeviceService(db)
    
    # Verify pump exists
    pump = await device_service.get_pump_by_pump_id(pump_id)
    if not pump:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pump not found"
        )
    
    data_service = DataService(db)
    logs = await data_service.get_pump_logs(
        pump_id=pump_id,
        start_time=start_time,
        end_time=end_time,
        status_filter=status_filter,
        skip=skip,
        limit=limit
    )
    return logs

@router.post("/{pump_id}/logs", response_model=PumpLog, status_code=status.HTTP_201_CREATED)
async def create_pump_log(
    pump_id: str,
    log: PumpLogCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new pump log entry"""
    device_service = DeviceService(db)
    
    # Verify pump exists
    pump = await device_service.get_pump_by_pump_id(pump_id)
    if not pump:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pump not found"
        )
    
    # Set pump_id from URL parameter
    log.pump_id = pump.id
    
    data_service = DataService(db)
    return await data_service.create_or_update_pump_log(log.dict())

@router.get("/{pump_id}/status")
async def get_pump_status(
    pump_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get current pump status including active transactions"""
    device_service = DeviceService(db)
    
    # Verify pump exists
    pump = await device_service.get_pump_by_pump_id(pump_id)
    if not pump:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pump not found"
        )
    
    data_service = DataService(db)
    
    # Get active transactions
    active_logs = await data_service.get_pump_logs(
        pump_id=pump_id,
        status_filter="started",
        limit=10
    )
    
    # Get recent completed transactions
    recent_logs = await data_service.get_pump_logs(
        pump_id=pump_id,
        status_filter="completed",
        limit=5
    )
    
    # Get nozzles
    nozzles = await device_service.get_nozzles_by_pump_id(pump.id)
    
    status_data = {
        "pump_id": pump_id,
        "pump_name": pump.name,
        "nozzles": [{"nozzle_id": n.nozzle_id, "name": n.name, "fuel_type": n.fuel_type} for n in nozzles],
        "active_transactions": len(active_logs),
        "active_logs": [log.dict() for log in active_logs],
        "recent_completed": [log.dict() for log in recent_logs]
    }
    
    return status_data
