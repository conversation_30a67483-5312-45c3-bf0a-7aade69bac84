from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from datetime import datetime

from database.connection import get_db
from models.pydantic.schemas import Tank, TankCreate, TankLog, TankLogCreate
from services.device_service import DeviceService
from services.data_service import DataService

router = APIRouter()

@router.get("/", response_model=List[Tank])
async def get_tanks(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get all tanks with pagination"""
    device_service = DeviceService(db)
    tanks = await device_service.get_tanks(skip=skip, limit=limit)
    return tanks

@router.get("/{tank_id}", response_model=Tank)
async def get_tank(
    tank_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get a specific tank by tank_id"""
    device_service = DeviceService(db)
    tank = await device_service.get_tank_by_tank_id(tank_id)
    if not tank:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tank not found"
        )
    return tank

@router.post("/", response_model=Tank, status_code=status.HTTP_201_CREATED)
async def create_tank(
    tank: TankCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new tank"""
    device_service = DeviceService(db)
    
    # Check if tank already exists
    existing_tank = await device_service.get_tank_by_tank_id(tank.tank_id)
    if existing_tank:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tank with this tank_id already exists"
        )
    
    return await device_service.create_tank(tank)

@router.get("/{tank_id}/logs", response_model=List[TankLog])
async def get_tank_logs(
    tank_id: str,
    start_time: Optional[datetime] = Query(None, description="Start time for log filtering"),
    end_time: Optional[datetime] = Query(None, description="End time for log filtering"),
    skip: int = 0,
    limit: int = 1000,
    db: AsyncSession = Depends(get_db)
):
    """Get tank logs with optional time filtering"""
    device_service = DeviceService(db)
    
    # Verify tank exists
    tank = await device_service.get_tank_by_tank_id(tank_id)
    if not tank:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tank not found"
        )
    
    data_service = DataService(db)
    logs = await data_service.get_tank_logs(
        tank_id=tank_id,
        start_time=start_time,
        end_time=end_time,
        skip=skip,
        limit=limit
    )
    return logs

@router.post("/{tank_id}/logs", response_model=TankLog, status_code=status.HTTP_201_CREATED)
async def create_tank_log(
    tank_id: str,
    log: TankLogCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new tank log entry"""
    device_service = DeviceService(db)
    
    # Verify tank exists
    tank = await device_service.get_tank_by_tank_id(tank_id)
    if not tank:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tank not found"
        )
    
    # Set tank_id from URL parameter
    log.tank_id = tank.id
    
    data_service = DataService(db)
    return await data_service.create_tank_log(log.dict())

@router.get("/{tank_id}/latest", response_model=Optional[TankLog])
async def get_latest_tank_log(
    tank_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get the latest tank log entry"""
    device_service = DeviceService(db)
    
    # Verify tank exists
    tank = await device_service.get_tank_by_tank_id(tank_id)
    if not tank:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tank not found"
        )
    
    data_service = DataService(db)
    latest_log = await data_service.get_latest_tank_log(tank_id)
    return latest_log

@router.get("/{tank_id}/status")
async def get_tank_status(
    tank_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get current tank status including latest readings"""
    device_service = DeviceService(db)
    
    # Verify tank exists
    tank = await device_service.get_tank_by_tank_id(tank_id)
    if not tank:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tank not found"
        )
    
    data_service = DataService(db)
    latest_log = await data_service.get_latest_tank_log(tank_id)
    
    status_data = {
        "tank_id": tank_id,
        "tank_name": tank.name,
        "capacity": tank.capacity,
        "fuel_type": tank.fuel_type,
        "latest_reading": latest_log.dict() if latest_log else None,
        "fill_percentage": None
    }
    
    # Calculate fill percentage if we have capacity and volume data
    if latest_log and tank.capacity and latest_log.volume:
        status_data["fill_percentage"] = (latest_log.volume / tank.capacity) * 100
    
    return status_data
