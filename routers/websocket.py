from fastapi import <PERSON><PERSON>out<PERSON>, WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import json
import asyncio
from typing import List, Dict, Any
import logging

from database.connection import get_db
from services.data_service import DataService

logger = logging.getLogger(__name__)

router = APIRouter()


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.subscriptions: Dict[WebSocket, List[str]] = {}

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.subscriptions[websocket] = []
        logger.info(
            f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if websocket in self.subscriptions:
            del self.subscriptions[websocket]
        logger.info(
            f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: str, topic: str = ""):
        """Broadcast message to all connected clients or those subscribed to a topic"""
        disconnected = []
        for websocket in self.active_connections:
            try:
                # If topic is specified, only send to subscribers
                if topic and topic not in self.subscriptions.get(websocket, []):
                    continue
                await websocket.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected.append(websocket)

        # Clean up disconnected websockets
        for websocket in disconnected:
            self.disconnect(websocket)

    def subscribe(self, websocket: WebSocket, topic: str):
        """Subscribe a websocket to a specific topic"""
        if websocket not in self.subscriptions:
            self.subscriptions[websocket] = []
        if topic not in self.subscriptions[websocket]:
            self.subscriptions[websocket].append(topic)

    def unsubscribe(self, websocket: WebSocket, topic: str):
        """Unsubscribe a websocket from a specific topic"""
        if websocket in self.subscriptions and topic in self.subscriptions[websocket]:
            self.subscriptions[websocket].remove(topic)


manager = ConnectionManager()


@router.websocket("/")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for real-time data"""
    await manager.connect(websocket)
    try:
        while True:
            # Receive messages from client
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                await handle_websocket_message(websocket, message)
            except json.JSONDecodeError:
                await manager.send_personal_message(
                    json.dumps({"error": "Invalid JSON format"}),
                    websocket
                )
    except WebSocketDisconnect:
        manager.disconnect(websocket)


async def handle_websocket_message(websocket: WebSocket, message: Dict[str, Any]):
    """Handle incoming WebSocket messages"""
    message_type = message.get("type")

    if message_type == "subscribe":
        topic = message.get("topic")
        if topic:
            manager.subscribe(websocket, topic)
            await manager.send_personal_message(
                json.dumps({"type": "subscription_confirmed", "topic": topic}),
                websocket
            )

    elif message_type == "unsubscribe":
        topic = message.get("topic")
        if topic:
            manager.unsubscribe(websocket, topic)
            await manager.send_personal_message(
                json.dumps(
                    {"type": "unsubscription_confirmed", "topic": topic}),
                websocket
            )

    elif message_type == "get_tank_status":
        tank_id = message.get("tank_id")
        if tank_id:
            # This would need a database session - simplified for now
            await manager.send_personal_message(
                json.dumps({
                    "type": "tank_status",
                    "tank_id": tank_id,
                    "data": {"message": "Tank status requested"}
                }),
                websocket
            )

    elif message_type == "get_pump_status":
        pump_id = message.get("pump_id")
        if pump_id:
            # This would need a database session - simplified for now
            await manager.send_personal_message(
                json.dumps({
                    "type": "pump_status",
                    "pump_id": pump_id,
                    "data": {"message": "Pump status requested"}
                }),
                websocket
            )

    else:
        await manager.send_personal_message(
            json.dumps({"error": f"Unknown message type: {message_type}"}),
            websocket
        )

# Functions to broadcast real-time updates (called from MQTT handlers)


async def broadcast_tank_update(tank_id: str, data: Dict[str, Any]):
    """Broadcast tank data update to subscribed clients"""
    message = json.dumps({
        "type": "tank_update",
        "tank_id": tank_id,
        "data": data,
        "timestamp": data.get("timestamp")
    })
    await manager.broadcast(message, f"tank:{tank_id}")


async def broadcast_pump_update(pump_id: str, data: Dict[str, Any]):
    """Broadcast pump data update to subscribed clients"""
    message = json.dumps({
        "type": "pump_update",
        "pump_id": pump_id,
        "data": data,
        "timestamp": data.get("timestamp")
    })
    await manager.broadcast(message, f"pump:{pump_id}")


async def broadcast_system_alert(alert_type: str, message: str, data: Dict[str, Any] = {}):
    """Broadcast system alert to all connected clients"""
    alert_message = json.dumps({
        "type": "system_alert",
        "alert_type": alert_type,
        "message": message,
        "data": data or {},
        "timestamp": data.get("timestamp") if data else None
    })
    await manager.broadcast(alert_message)
