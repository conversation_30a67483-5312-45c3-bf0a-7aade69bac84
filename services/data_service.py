from urllib import request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, desc, and_
from typing import List, Optional, Dict, Any
from datetime import datetime

from models.sqlalchemy.models import Tank<PERSON>og, PumpLog, Tank, Pump
from models.pydantic.schemas import TankLogCreate, PumpLogCreate, PumpLogUpdate
from utils.redis_client import redis_client


class DataService:
    def __init__(self, db: AsyncSession = None):
        self.base_url = config("SMARTEYE_BASE_URL")

    # Push tank logs to smarteye core
    # async def create_tank_log(self, tank_log_data: Dict[str, Any]) -> TankLog:
    #     """Create a new tank log entry"""
    #     # First, get the tank by tank_id to get the database ID
    #     if isinstance(tank_log_data.get("tank_id"), str):
    #         tank_result = await self.db.execute(
    #             select(Tank).where(Tank.tank_id == tank_log_data["tank_id"])
    #         )
    #         tank = tank_result.scalar_one_or_none()
    #         if tank:
    #             tank_log_data["tank_id"] = tank.id
    #         else:
    #             raise ValueError(
    #                 f"Tank with tank_id {tank_log_data['tank_id']} not found")

    #     db_tank_log = TankLog(**tank_log_data)
    #     self.db.add(db_tank_log)
    #     await self.db.commit()
    #     await self.db.refresh(db_tank_log)

    #     # Cache latest tank reading in Redis
    #     await self._cache_latest_tank_reading(tank_log_data["tank_id"], tank_log_data)

    #     return db_tank_log

    # Pump logs to smarteye
    async def send_pump_transaction_logs(self, pump_log_data: Dict[str, Any]) -> PumpLog:
        """We will be sending the pump logs to smarteye"""
        return request.post(self.base_url+"/smartpump/transaction_logger/",  # type: ignore
                            json=pump_log_data)

    async def send_pump_pic_transaction_logs(self, pump_log_data: List[List]) -> PumpLog:
        """We will be sending the pump logs to smarteye"""
        return request.post(self.base_url+"/smartpump/pic_transaction_logger/",  # type: ignore
                            json=pump_log_data)

    # Nozzle status operations

    async def update_nozzle_status(self, nozzle_id: str, status_data: Dict[str, Any]):
        """Update nozzle status in cache"""
        await redis_client.set_nozzle_status(nozzle_id, status_data)

    async def get_nozzle_status(self, nozzle_id: str) -> Optional[Dict[str, Any]]:
        """Get nozzle status from cache"""
        return await redis_client.get_nozzle_status(nozzle_id)

    # Cache operations
    async def _cache_latest_tank_reading(self, tank_id: int, tank_data: Dict[str, Any]):
        """Cache the latest tank reading in Redis"""
        await redis_client.set_latest_tank_reading(tank_id, tank_data)
