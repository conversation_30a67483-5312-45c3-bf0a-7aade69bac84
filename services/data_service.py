import os
import logging
import json
from urllib import request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

from models.sqlalchemy.models import TankLog, Tank
from models.pydantic.schemas import TankLogCreate
from utils.redis_client import redis_client

logger = logging.getLogger(__name__)


class DataService:
    def __init__(self, db: AsyncSession = None):
        self.base_url = os.getenv("SMARTEYE_BASE_URL", "http://localhost:8000")

    # Push tank logs to smarteye core
    # async def create_tank_log(self, tank_log_data: Dict[str, Any]) -> TankLog:
    #     """Create a new tank log entry"""
    #     # First, get the tank by tank_id to get the database ID
    #     if isinstance(tank_log_data.get("tank_id"), str):
    #         tank_result = await self.db.execute(
    #             select(Tank).where(Tank.tank_id == tank_log_data["tank_id"])
    #         )
    #         tank = tank_result.scalar_one_or_none()
    #         if tank:
    #             tank_log_data["tank_id"] = tank.id
    #         else:
    #             raise ValueError(
    #                 f"Tank with tank_id {tank_log_data['tank_id']} not found")

    #     db_tank_log = TankLog(**tank_log_data)
    #     self.db.add(db_tank_log)
    #     await self.db.commit()
    #     await self.db.refresh(db_tank_log)

    #     # Cache latest tank reading in Redis
    #     await self._cache_latest_tank_reading(tank_log_data["tank_id"], tank_log_data)

    #     return db_tank_log

    # Pump logs to smarteye
    async def send_pump_transaction_logs(self, pump_log_data: List[Dict[str, Any]]) -> bool:
        """Send pump transaction logs to smarteye core"""
        try:
            if not pump_log_data:
                logger.warning("Empty pump transaction log data provided")
                return False

            endpoint = f"{self.base_url}/smartpump/transaction_logger/"
            logger.info(
                f"Sending {len(pump_log_data)} pump transaction logs to {endpoint}")

            # Using urllib.request for now, but this should be replaced with aiohttp for async
            import json
            response = request.urlopen(
                request.Request(
                    endpoint,
                    data=json.dumps(pump_log_data).encode('utf-8'),
                    headers={'Content-Type': 'application/json'}
                )
            )

            if response.status == 200:
                logger.info("Successfully sent pump transaction logs")
                return True
            else:
                logger.error(
                    f"Failed to send pump transaction logs. Status: {response.status}")
                return False

        except Exception as e:
            logger.error(f"Error sending pump transaction logs: {e}")
            return False

    async def send_pump_pic_transaction_logs(self, pump_log_data: List[List]) -> bool:
        """Send pump PIC transaction logs to smarteye core"""
        try:
            if not pump_log_data:
                logger.warning("Empty pump PIC transaction log data provided")
                return False

            endpoint = f"{self.base_url}/smartpump/pic_transaction_logger/"
            logger.info(
                f"Sending {len(pump_log_data)} pump PIC transaction logs to {endpoint}")

            # Using urllib.request for now, but this should be replaced with aiohttp for async
            import json
            response = request.urlopen(
                request.Request(
                    endpoint,
                    data=json.dumps(pump_log_data).encode('utf-8'),
                    headers={'Content-Type': 'application/json'}
                )
            )

            if response.status == 200:
                logger.info("Successfully sent pump PIC transaction logs")
                return True
            else:
                logger.error(
                    f"Failed to send pump PIC transaction logs. Status: {response.status}")
                return False

        except Exception as e:
            logger.error(f"Error sending pump PIC transaction logs: {e}")
            return False

    # Nozzle status operations

    async def update_nozzle_status(self, nozzle_id: str, status_data: Dict[str, Any]):
        """Update nozzle status in cache"""
        await redis_client.set_nozzle_status(nozzle_id, status_data)

    async def get_nozzle_status(self, nozzle_id: str) -> Optional[Dict[str, Any]]:
        """Get nozzle status from cache"""
        return await redis_client.get_nozzle_status(nozzle_id)

    # Cache operations
    async def _cache_latest_tank_reading(self, tank_id: int, tank_data: Dict[str, Any]):
        """Cache the latest tank reading in Redis"""
        await redis_client.set_latest_tank_reading(tank_id, tank_data)
