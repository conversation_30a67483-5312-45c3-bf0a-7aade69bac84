from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from typing import List, Optional
from datetime import datetime

from models.sqlalchemy.models import Device, Tank, Pump, Nozzle, Company, Site
from models.pydantic.schemas import (
    DeviceCreate, DeviceUpdate, TankCreate, PumpCreate, NozzleCreate,
    CompanyCreate, SiteCreate
)

class DeviceService:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    # Device operations
    async def get_devices(self, skip: int = 0, limit: int = 100) -> List[Device]:
        """Get all devices with pagination"""
        result = await self.db.execute(
            select(Device)
            .options(selectinload(Device.site))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_device_by_id(self, device_id: int) -> Optional[Device]:
        """Get device by database ID"""
        result = await self.db.execute(
            select(Device)
            .options(selectinload(Device.site))
            .where(Device.id == device_id)
        )
        return result.scalar_one_or_none()
    
    async def get_device_by_device_id(self, device_id: str) -> Optional[Device]:
        """Get device by device_id string"""
        result = await self.db.execute(
            select(Device)
            .options(selectinload(Device.site))
            .where(Device.device_id == device_id)
        )
        return result.scalar_one_or_none()
    
    async def create_device(self, device: DeviceCreate) -> Device:
        """Create a new device"""
        db_device = Device(**device.dict())
        self.db.add(db_device)
        await self.db.commit()
        await self.db.refresh(db_device)
        return db_device
    
    async def update_device(self, device_id: int, device_update: DeviceUpdate) -> Device:
        """Update a device"""
        update_data = device_update.dict(exclude_unset=True)
        update_data["updated_at"] = datetime.now()
        
        await self.db.execute(
            update(Device)
            .where(Device.id == device_id)
            .values(**update_data)
        )
        await self.db.commit()
        
        return await self.get_device_by_id(device_id)
    
    async def delete_device(self, device_id: int):
        """Delete a device"""
        await self.db.execute(
            delete(Device).where(Device.id == device_id)
        )
        await self.db.commit()
    
    async def update_device_last_seen(self, device_id: str):
        """Update device last seen timestamp"""
        await self.db.execute(
            update(Device)
            .where(Device.device_id == device_id)
            .values(last_seen=datetime.now())
        )
        await self.db.commit()
    
    # Tank operations
    async def get_tanks(self, skip: int = 0, limit: int = 100) -> List[Tank]:
        """Get all tanks with pagination"""
        result = await self.db.execute(
            select(Tank)
            .options(selectinload(Tank.device))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_tank_by_tank_id(self, tank_id: str) -> Optional[Tank]:
        """Get tank by tank_id string"""
        result = await self.db.execute(
            select(Tank)
            .options(selectinload(Tank.device))
            .where(Tank.tank_id == tank_id)
        )
        return result.scalar_one_or_none()
    
    async def create_tank(self, tank: TankCreate) -> Tank:
        """Create a new tank"""
        db_tank = Tank(**tank.dict())
        self.db.add(db_tank)
        await self.db.commit()
        await self.db.refresh(db_tank)
        return db_tank
    
    # Pump operations
    async def get_pumps(self, skip: int = 0, limit: int = 100) -> List[Pump]:
        """Get all pumps with pagination"""
        result = await self.db.execute(
            select(Pump)
            .options(selectinload(Pump.device))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def get_pump_by_pump_id(self, pump_id: str) -> Optional[Pump]:
        """Get pump by pump_id string"""
        result = await self.db.execute(
            select(Pump)
            .options(selectinload(Pump.device))
            .where(Pump.pump_id == pump_id)
        )
        return result.scalar_one_or_none()
    
    async def create_pump(self, pump: PumpCreate) -> Pump:
        """Create a new pump"""
        db_pump = Pump(**pump.dict())
        self.db.add(db_pump)
        await self.db.commit()
        await self.db.refresh(db_pump)
        return db_pump
    
    # Nozzle operations
    async def get_nozzles_by_pump_id(self, pump_id: int) -> List[Nozzle]:
        """Get all nozzles for a pump"""
        result = await self.db.execute(
            select(Nozzle).where(Nozzle.pump_id == pump_id)
        )
        return result.scalars().all()
    
    async def get_nozzle_by_nozzle_id(self, nozzle_id: str) -> Optional[Nozzle]:
        """Get nozzle by nozzle_id string"""
        result = await self.db.execute(
            select(Nozzle).where(Nozzle.nozzle_id == nozzle_id)
        )
        return result.scalar_one_or_none()
    
    async def create_nozzle(self, nozzle: NozzleCreate) -> Nozzle:
        """Create a new nozzle"""
        db_nozzle = Nozzle(**nozzle.dict())
        self.db.add(db_nozzle)
        await self.db.commit()
        await self.db.refresh(db_nozzle)
        return db_nozzle
    
    # Company operations
    async def create_company(self, company: CompanyCreate) -> Company:
        """Create a new company"""
        db_company = Company(**company.dict())
        self.db.add(db_company)
        await self.db.commit()
        await self.db.refresh(db_company)
        return db_company
    
    async def get_company_by_code(self, code: str) -> Optional[Company]:
        """Get company by code"""
        result = await self.db.execute(
            select(Company).where(Company.code == code)
        )
        return result.scalar_one_or_none()
    
    # Site operations
    async def create_site(self, site: SiteCreate) -> Site:
        """Create a new site"""
        db_site = Site(**site.dict())
        self.db.add(db_site)
        await self.db.commit()
        await self.db.refresh(db_site)
        return db_site
    
    async def get_sites_by_company_id(self, company_id: int) -> List[Site]:
        """Get all sites for a company"""
        result = await self.db.execute(
            select(Site).where(Site.company_id == company_id)
        )
        return result.scalars().all()
