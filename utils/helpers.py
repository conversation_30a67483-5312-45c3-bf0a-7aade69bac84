import json
import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
import hashlib
import secrets

logger = logging.getLogger(__name__)

class DateTimeEncoder(json.JSONEncoder):
    """JSON encoder that handles datetime objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

def generate_transaction_id(pump_id: str, nozzle_id: str) -> str:
    """Generate a unique transaction ID"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = secrets.token_hex(4)
    return f"{pump_id}_{nozzle_id}_{timestamp}_{random_suffix}"

def generate_device_hash(device_data: Dict[str, Any]) -> str:
    """Generate a hash for device data"""
    # Sort keys to ensure consistent hashing
    sorted_data = json.dumps(device_data, sort_keys=True, cls=DateTimeEncoder)
    return hashlib.sha256(sorted_data.encode()).hexdigest()[:16]

def calculate_fuel_percentage(current_volume: float, capacity: float) -> float:
    """Calculate fuel percentage"""
    if capacity <= 0:
        return 0.0
    percentage = (current_volume / capacity) * 100
    return max(0.0, min(100.0, percentage))  # Clamp between 0 and 100

def calculate_fuel_consumption_rate(
    logs: List[Dict[str, Any]], 
    time_window_hours: int = 24
) -> Optional[float]:
    """Calculate fuel consumption rate in liters per hour"""
    if len(logs) < 2:
        return None
    
    # Sort logs by timestamp
    sorted_logs = sorted(logs, key=lambda x: x.get("timestamp", datetime.min))
    
    if len(sorted_logs) < 2:
        return None
    
    first_log = sorted_logs[0]
    last_log = sorted_logs[-1]
    
    first_volume = first_log.get("volume")
    last_volume = last_log.get("volume")
    first_time = first_log.get("timestamp")
    last_time = last_log.get("timestamp")
    
    if None in [first_volume, last_volume, first_time, last_time]:
        return None
    
    # Calculate time difference in hours
    time_diff = (last_time - first_time).total_seconds() / 3600
    
    if time_diff <= 0:
        return None
    
    # Calculate consumption (negative because volume decreases)
    volume_diff = first_volume - last_volume
    consumption_rate = volume_diff / time_diff
    
    return max(0.0, consumption_rate)  # Ensure non-negative

def format_alert_message(
    alert_type: str, 
    device_id: str, 
    current_value: float, 
    threshold: float,
    unit: str = ""
) -> str:
    """Format alert message"""
    messages = {
        "low_fuel": f"Low fuel alert for {device_id}: {current_value}{unit} (threshold: {threshold}{unit})",
        "high_temperature": f"High temperature alert for {device_id}: {current_value}{unit} (threshold: {threshold}{unit})",
        "water_detected": f"Water detected in tank {device_id}: {current_value}{unit}",
        "device_offline": f"Device {device_id} is offline",
        "sensor_error": f"Sensor error detected for {device_id}",
        "dispense_error": f"Dispense error for pump {device_id}"
    }
    
    return messages.get(alert_type, f"Alert for {device_id}: {alert_type}")

def convert_temperature(temp_celsius: float, to_unit: str = "fahrenheit") -> float:
    """Convert temperature between units"""
    if to_unit.lower() == "fahrenheit":
        return (temp_celsius * 9/5) + 32
    elif to_unit.lower() == "kelvin":
        return temp_celsius + 273.15
    else:
        return temp_celsius  # Return as Celsius

def convert_volume(volume_liters: float, to_unit: str = "gallons") -> float:
    """Convert volume between units"""
    conversions = {
        "gallons": 0.264172,  # US gallons
        "imperial_gallons": 0.219969,  # Imperial gallons
        "cubic_meters": 0.001,
        "cubic_feet": 0.0353147
    }
    
    multiplier = conversions.get(to_unit.lower(), 1.0)
    return volume_liters * multiplier

def parse_mqtt_timestamp(timestamp_str: str) -> datetime:
    """Parse timestamp from MQTT message"""
    try:
        # Try ISO format first
        if timestamp_str.endswith('Z'):
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        else:
            return datetime.fromisoformat(timestamp_str)
    except ValueError:
        try:
            # Try Unix timestamp
            return datetime.fromtimestamp(float(timestamp_str), tz=timezone.utc)
        except (ValueError, TypeError):
            # Return current time as fallback
            logger.warning(f"Could not parse timestamp: {timestamp_str}")
            return datetime.now(timezone.utc)

def create_device_summary(device_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a summary of device data"""
    summary = {
        "device_id": device_data.get("device_id"),
        "device_type": device_data.get("device_type"),
        "name": device_data.get("name"),
        "is_active": device_data.get("is_active", False),
        "last_seen": device_data.get("last_seen"),
        "status": "online" if device_data.get("is_active") else "offline"
    }
    
    # Add type-specific information
    if device_data.get("device_type") == "tank":
        summary.update({
            "capacity": device_data.get("capacity"),
            "fuel_type": device_data.get("fuel_type"),
            "current_volume": device_data.get("current_volume"),
            "fill_percentage": device_data.get("fill_percentage")
        })
    elif device_data.get("device_type") == "pump":
        summary.update({
            "nozzle_count": device_data.get("nozzle_count", 0),
            "active_transactions": device_data.get("active_transactions", 0)
        })
    
    return summary

def validate_numeric_range(value: Union[int, float], min_val: float, max_val: float) -> bool:
    """Validate if a numeric value is within range"""
    try:
        num_value = float(value)
        return min_val <= num_value <= max_val
    except (ValueError, TypeError):
        return False

def safe_float_conversion(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int_conversion(value: Any, default: int = 0) -> int:
    """Safely convert value to int"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def truncate_string(text: str, max_length: int = 100) -> str:
    """Truncate string to maximum length"""
    if not isinstance(text, str):
        text = str(text)
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."

def get_time_bucket(timestamp: datetime, bucket_size_minutes: int = 5) -> datetime:
    """Get time bucket for aggregating data"""
    minutes = (timestamp.minute // bucket_size_minutes) * bucket_size_minutes
    return timestamp.replace(minute=minutes, second=0, microsecond=0)
