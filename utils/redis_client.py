import redis.asyncio as redis
import json
import os
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
import logging

load_dotenv()

logger = logging.getLogger(__name__)


class RedisClient:
    def __init__(self):
        self.host = os.getenv("REDIS_HOST", "localhost")
        self.port = int(os.getenv("REDIS_PORT", "6379"))
        self.db = 0
        self.redis_client = None

    async def connect(self):
        """Connect to Redis"""
        try:
            self.redis_client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.db,
                decode_responses=True
            )
            # Test connection
            await self.redis_client.ping()
            logger.info(f"Connected to Redis at {self.host}:{self.port}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise

    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Disconnected from Redis")

    async def get_client(self):
        """Get Redis client, connect if not connected"""
        if not self.redis_client:
            await self.connect()
        return self.redis_client

    # Tank operations
    async def set_latest_tank_reading(self, tank_id: str, data: Dict[str, Any], ttl: int = 3600):
        """Cache latest tank reading"""
        client = await self.get_client()
        key = f"tank:latest:{tank_id}"
        # type: ignore
        await client.setex(key, ttl, json.dumps(data, default=str))

    async def get_latest_tank_reading(self, tank_id: str) -> Optional[Dict[str, Any]]:
        """Get latest tank reading from cache"""
        client = await self.get_client()
        key = f"tank:latest:{tank_id}"
        data = await client.get(key)  # type: ignore
        if data:
            return json.loads(data)
        return None

    async def set_tank_alert(self, tank_id: str, alert_type: str, alert_data: Dict[str, Any], ttl: int = 86400):
        """Set tank alert"""
        client = await self.get_client()
        key = f"tank:alert:{tank_id}:{alert_type}"
        # type: ignore
        await client.setex(key, ttl, json.dumps(alert_data, default=str))

    async def get_tank_alerts(self, tank_id: str) -> List[Dict[str, Any]]:
        """Get all alerts for a tank"""
        client = await self.get_client()
        pattern = f"tank:alert:{tank_id}:*"
        keys = await client.keys(pattern)  # type: ignore
        alerts = []
        for key in keys:
            data = await client.get(key)  # type: ignore
            if data:
                alert = json.loads(data)
                alert["alert_type"] = key.split(":")[-1]
                alerts.append(alert)
        return alerts

    # Pump operations
    async def set_pump_status(self, pump_id: str, status_data: Dict[str, Any], ttl: int = 300):
        """Cache pump status"""
        client = await self.get_client()
        key = f"pump:status:{pump_id}"
        # type: ignore
        await client.setex(key, ttl, json.dumps(status_data, default=str))

    async def get_pump_status(self, pump_id: str) -> Optional[Dict[str, Any]]:
        """Get pump status from cache"""
        client = await self.get_client()
        key = f"pump:status:{pump_id}"
        data = await client.get(key)  # type: ignore
        if data:
            return json.loads(data)
        return None

    # Nozzle operations
    async def set_nozzle_status(self, nozzle_id: str, status_data: Dict[str, Any], ttl: int = 300):
        """Cache nozzle status"""
        client = await self.get_client()
        key = f"nozzle:status:{nozzle_id}"
        # type: ignore
        await client.setex(key, ttl, json.dumps(status_data, default=str))

    async def get_nozzle_status(self, nozzle_id: str) -> Optional[Dict[str, Any]]:
        """Get nozzle status from cache"""
        client = await self.get_client()
        key = f"nozzle:status:{nozzle_id}"
        data = await client.get(key)  # type: ignore
        if data:
            return json.loads(data)
        return None

    # Device operations
    async def set_device_online(self, device_id: str, ttl: int = 300):
        """Mark device as online"""
        client = await self.get_client()
        key = f"device:online:{device_id}"
        await client.setex(key, ttl, "1")  # type: ignore

    async def is_device_online(self, device_id: str) -> bool:
        """Check if device is online"""
        client = await self.get_client()
        key = f"device:online:{device_id}"
        return await client.exists(key) > 0  # type: ignore

    async def get_online_devices(self) -> List[str]:
        """Get list of online devices"""
        client = await self.get_client()
        pattern = "device:online:*"
        keys = await client.keys(pattern)  # type: ignore
        return [key.split(":")[-1] for key in keys]

    # Configuration cache
    async def set_device_config(self, device_id: str, config: Dict[str, Any], ttl: int = 86400):
        """Cache device configuration"""
        client = await self.get_client()
        key = f"device:config:{device_id}"
        # type: ignore
        await client.setex(key, ttl, json.dumps(config, default=str))

    async def get_device_config(self, device_id: str) -> Optional[Dict[str, Any]]:
        """Get device configuration from cache"""
        client = await self.get_client()
        key = f"device:config:{device_id}"
        data = await client.get(key)  # type: ignore
        if data:
            return json.loads(data)
        return None

    # System operations
    async def set_system_status(self, status_data: Dict[str, Any], ttl: int = 60):
        """Cache system status"""
        client = await self.get_client()
        key = "system:status"
        # type: ignore
        await client.setex(key, ttl, json.dumps(status_data, default=str))

    async def get_system_status(self) -> Optional[Dict[str, Any]]:
        """Get system status from cache"""
        client = await self.get_client()
        key = "system:status"
        data = await client.get(key)  # type: ignore
        if data:
            return json.loads(data)
        return None

    # Pub/Sub operations
    async def publish_message(self, channel: str, message: Dict[str, Any]):
        """Publish message to Redis channel"""
        client = await self.get_client()
        # type: ignore
        # type: ignore
        await client.publish(channel, json.dumps(message, default=str))

    async def subscribe_to_channel(self, channel: str):
        """Subscribe to Redis channel"""
        client = await self.get_client()
        pubsub = client.pubsub()  # type: ignore
        await pubsub.subscribe(channel)
        return pubsub


# Global Redis client instance
redis_client = RedisClient()
