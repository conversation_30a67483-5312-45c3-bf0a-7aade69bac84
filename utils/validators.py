import re
from typing import Any, Dict, List, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom validation error"""
    pass

class DataValidator:
    """Data validation utilities for IoT device data"""
    
    @staticmethod
    def validate_device_id(device_id: str) -> bool:
        """Validate device ID format"""
        if not device_id or not isinstance(device_id, str):
            return False
        # Device ID should be alphanumeric with optional hyphens/underscores
        pattern = r'^[a-zA-Z0-9_-]+$'
        return bool(re.match(pattern, device_id)) and len(device_id) <= 100
    
    @staticmethod
    def validate_tank_id(tank_id: str) -> bool:
        """Validate tank ID format"""
        if not tank_id or not isinstance(tank_id, str):
            return False
        pattern = r'^[a-zA-Z0-9_-]+$'
        return bool(re.match(pattern, tank_id)) and len(tank_id) <= 100
    
    @staticmethod
    def validate_pump_id(pump_id: str) -> bool:
        """Validate pump ID format"""
        if not pump_id or not isinstance(pump_id, str):
            return False
        pattern = r'^[a-zA-Z0-9_-]+$'
        return bool(re.match(pattern, pump_id)) and len(pump_id) <= 100
    
    @staticmethod
    def validate_nozzle_id(nozzle_id: str) -> bool:
        """Validate nozzle ID format"""
        if not nozzle_id or not isinstance(nozzle_id, str):
            return False
        pattern = r'^[a-zA-Z0-9_-]+$'
        return bool(re.match(pattern, nozzle_id)) and len(nozzle_id) <= 100
    
    @staticmethod
    def validate_tank_log_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize tank log data"""
        validated_data = {}
        
        # Required fields
        if "tank_id" not in data:
            raise ValidationError("tank_id is required")
        
        if not DataValidator.validate_tank_id(data["tank_id"]):
            raise ValidationError("Invalid tank_id format")
        validated_data["tank_id"] = data["tank_id"]
        
        # Timestamp
        timestamp = data.get("timestamp")
        if timestamp:
            if isinstance(timestamp, str):
                try:
                    validated_data["timestamp"] = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    raise ValidationError("Invalid timestamp format")
            elif isinstance(timestamp, datetime):
                validated_data["timestamp"] = timestamp
            else:
                raise ValidationError("Invalid timestamp type")
        else:
            validated_data["timestamp"] = datetime.now()
        
        # Numeric fields with validation
        numeric_fields = {
            "volume": (0, 100000),  # 0 to 100,000 liters
            "temperature": (-50, 100),  # -50 to 100 Celsius
            "water_level": (0, 10000),  # 0 to 10,000 mm
            "fuel_height": (0, 10000)   # 0 to 10,000 mm
        }
        
        for field, (min_val, max_val) in numeric_fields.items():
            value = data.get(field)
            if value is not None:
                try:
                    value = float(value)
                    if min_val <= value <= max_val:
                        validated_data[field] = value
                    else:
                        logger.warning(f"Tank log {field} value {value} out of range [{min_val}, {max_val}]")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid tank log {field} value: {value}")
        
        return validated_data
    
    @staticmethod
    def validate_pump_log_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize pump log data"""
        validated_data = {}
        
        # Required fields
        required_fields = ["pump_id", "nozzle_id", "transaction_id"]
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"{field} is required")
            validated_data[field] = str(data[field])
        
        # Validate IDs
        if not DataValidator.validate_pump_id(validated_data["pump_id"]):
            raise ValidationError("Invalid pump_id format")
        
        if not DataValidator.validate_nozzle_id(validated_data["nozzle_id"]):
            raise ValidationError("Invalid nozzle_id format")
        
        # Transaction ID validation
        if len(validated_data["transaction_id"]) > 100:
            raise ValidationError("transaction_id too long")
        
        # Timestamps
        for field in ["start_time", "end_time"]:
            timestamp = data.get(field)
            if timestamp:
                if isinstance(timestamp, str):
                    try:
                        validated_data[field] = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except ValueError:
                        raise ValidationError(f"Invalid {field} format")
                elif isinstance(timestamp, datetime):
                    validated_data[field] = timestamp
        
        # Numeric fields with validation
        numeric_fields = {
            "volume_dispensed": (0, 1000),  # 0 to 1,000 liters per transaction
            "amount": (0, 100000),  # 0 to 100,000 currency units
            "price_per_liter": (0, 1000)   # 0 to 1,000 currency units per liter
        }
        
        for field, (min_val, max_val) in numeric_fields.items():
            value = data.get(field)
            if value is not None:
                try:
                    value = float(value)
                    if min_val <= value <= max_val:
                        validated_data[field] = value
                    else:
                        logger.warning(f"Pump log {field} value {value} out of range [{min_val}, {max_val}]")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid pump log {field} value: {value}")
        
        # Status validation
        status = data.get("status")
        if status:
            valid_statuses = ["started", "completed", "cancelled"]
            if status in valid_statuses:
                validated_data["status"] = status
            else:
                logger.warning(f"Invalid pump log status: {status}")
                validated_data["status"] = "started"
        
        return validated_data
    
    @staticmethod
    def validate_device_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate device configuration data"""
        if not isinstance(config, dict):
            raise ValidationError("Configuration must be a dictionary")
        
        # Basic validation - ensure no dangerous keys
        dangerous_keys = ["__", "eval", "exec", "import"]
        for key in config.keys():
            if any(dangerous in str(key).lower() for dangerous in dangerous_keys):
                raise ValidationError(f"Invalid configuration key: {key}")
        
        # Limit configuration size
        config_str = str(config)
        if len(config_str) > 10000:  # 10KB limit
            raise ValidationError("Configuration too large")
        
        return config
    
    @staticmethod
    def sanitize_mqtt_topic(topic: str) -> str:
        """Sanitize MQTT topic"""
        if not topic or not isinstance(topic, str):
            raise ValidationError("Invalid topic")
        
        # Remove dangerous characters
        sanitized = re.sub(r'[^\w/\-\.]', '', topic)
        
        # Limit length
        if len(sanitized) > 200:
            raise ValidationError("Topic too long")
        
        return sanitized
    
    @staticmethod
    def validate_time_range(start_time: Optional[datetime], end_time: Optional[datetime]) -> bool:
        """Validate time range"""
        if start_time and end_time:
            if start_time >= end_time:
                return False
            
            # Check if range is reasonable (not more than 1 year)
            if (end_time - start_time).days > 365:
                return False
        
        return True
